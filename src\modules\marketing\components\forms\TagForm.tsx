import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Select,
  Typography,
} from '@/shared/components/common';
import { z } from 'zod';

// Enum cho trạng thái
enum Status {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

// Schema factory cho form với hỗ trợ i18n
const createFormSchema = (t: (key: string, fallback?: string) => string) => z.object({
  name: z.string().min(1, t('marketing:tag.validation.nameRequired', 'Tên tag là bắt buộc')),
  description: z.string().optional(),
  status: z.nativeEnum(Status, {
    errorMap: () => ({ message: t('marketing:tag.validation.statusRequired', 'Trạng thái là bắt buộc') })
  }),
});

export type TagFormValues = z.infer<ReturnType<typeof createFormSchema>>;

interface TagFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
}

/**
 * Component form thêm/sửa tag
 */
const TagForm: React.FC<TagFormProps> = ({ onSubmit, onCancel }) => {
  const { t } = useTranslation(['marketing', 'common']);

  // Tạo schema với hỗ trợ i18n
  const formSchema = React.useMemo(() => createFormSchema(t), [t]);

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        {t('marketing:tags.addNew', 'Thêm tag mới')}
      </Typography>

      <Form schema={formSchema} onSubmit={onSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="name" label={t('marketing:tags.form.name', 'Tên tag')} required>
            <Input placeholder={t('marketing:tags.form.namePlaceholder', 'Nhập tên tag')} fullWidth />
          </FormItem>

          <FormItem name="status" label={t('marketing:tags.form.status', 'Trạng thái')} required>
            <Select
              options={[
                { value: Status.ACTIVE, label: t('marketing:tags.status.active', 'Hoạt động') },
                { value: Status.INACTIVE, label: t('marketing:tags.status.inactive', 'Không hoạt động') },
              ]}
              placeholder={t('marketing:tags.form.statusPlaceholder', 'Chọn trạng thái')}
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem name="description" label={t('marketing:tags.form.description', 'Mô tả')}>
          <Input placeholder={t('marketing:tags.form.descriptionPlaceholder', 'Nhập mô tả')} fullWidth />
        </FormItem>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onCancel}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" variant="primary">
            {t('common:save', 'Lưu')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default TagForm;
