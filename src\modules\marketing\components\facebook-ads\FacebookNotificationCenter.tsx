import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Badge,
  Modal,
  Tabs,
} from '@/shared/components/common';


interface Notification {
  id: string;
  type: 'alert' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  actionRequired?: boolean;
  relatedId?: string;
  relatedType?: 'account' | 'campaign' | 'adset' | 'ad';
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface FacebookNotificationCenterProps {
  /**
   * Hiển thị loading state
   */
  isLoading?: boolean;
  
  /**
   * Callback khi click vào notification
   */
  onNotificationClick?: (notification: Notification) => void;
  
  /**
   * Callback khi mark as read
   */
  onMarkAsRead?: (notificationId: string) => void;
  
  /**
   * Callback khi mark all as read
   */
  onMarkAllAsRead?: () => void;
  
  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Facebook Notification Center Component
 * Trung tâm thông báo và cảnh báo cho Facebook Ads
 */
const FacebookNotificationCenter: React.FC<FacebookNotificationCenterProps> = ({
  onNotificationClick,
  onMarkAsRead,
  onMarkAllAsRead,
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  const [showModal, setShowModal] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  // Mock notifications - In real app, this would come from API
  const notifications: Notification[] = useMemo(() => {
    const mockNotifications: Notification[] = [
      {
        id: '1',
        type: 'alert',
        title: t('marketing:facebookAds.notifications.budgetAlert', 'Cảnh báo ngân sách'),
        message: t('marketing:facebookAds.notifications.budgetAlertMessage', 'Chiến dịch "Summer Sale 2024" sắp hết ngân sách (còn 5%)'),
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        isRead: false,
        actionRequired: true,
        relatedId: 'campaign_1',
        relatedType: 'campaign',
        severity: 'high',
      },
      {
        id: '2',
        type: 'warning',
        title: t('marketing:facebookAds.notifications.performanceWarning', 'Cảnh báo hiệu suất'),
        message: t('marketing:facebookAds.notifications.performanceWarningMessage', 'CTR của chiến dịch "Brand Awareness" giảm 25% so với tuần trước'),
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        isRead: false,
        actionRequired: true,
        relatedId: 'campaign_2',
        relatedType: 'campaign',
        severity: 'medium',
      },
      {
        id: '3',
        type: 'info',
        title: t('marketing:facebookAds.notifications.accountSync', 'Đồng bộ tài khoản'),
        message: t('marketing:facebookAds.notifications.accountSyncMessage', 'Tài khoản Facebook Ads đã được đồng bộ thành công'),
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        isRead: true,
        actionRequired: false,
        relatedId: 'account_1',
        relatedType: 'account',
        severity: 'low',
      },
      {
        id: '4',
        type: 'success',
        title: t('marketing:facebookAds.notifications.campaignApproved', 'Chiến dịch được phê duyệt'),
        message: t('marketing:facebookAds.notifications.campaignApprovedMessage', 'Chiến dịch "Product Launch" đã được Facebook phê duyệt và bắt đầu chạy'),
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        isRead: true,
        actionRequired: false,
        relatedId: 'campaign_3',
        relatedType: 'campaign',
        severity: 'low',
      },
      {
        id: '5',
        type: 'alert',
        title: t('marketing:facebookAds.notifications.accountLimit', 'Giới hạn tài khoản'),
        message: t('marketing:facebookAds.notifications.accountLimitMessage', 'Tài khoản quảng cáo đã đạt 90% giới hạn chi tiêu hàng ngày'),
        timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
        isRead: false,
        actionRequired: true,
        relatedId: 'account_1',
        relatedType: 'account',
        severity: 'critical',
      },
    ];

    return mockNotifications;
  }, [t]);

  // Filter notifications by tab
  const filteredNotifications = useMemo(() => {
    switch (activeTab) {
      case 'unread':
        return notifications.filter(n => !n.isRead);
      case 'alerts':
        return notifications.filter(n => n.type === 'alert' || n.severity === 'critical');
      case 'warnings':
        return notifications.filter(n => n.type === 'warning');
      default:
        return notifications;
    }
  }, [notifications, activeTab]);

  // Count unread notifications
  const unreadCount = notifications.filter(n => !n.isRead).length;
  const alertCount = notifications.filter(n => n.type === 'alert' || n.severity === 'critical').length;

  // Get notification icon and color
  const getNotificationConfig = (notification: Notification) => {
    switch (notification.type) {
      case 'alert':
        return {
          icon: 'alert-triangle',
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
        };
      case 'warning':
        return {
          icon: 'alert-circle',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
        };
      case 'info':
        return {
          icon: 'info',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
        };
      case 'success':
        return {
          icon: 'check-circle',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
        };
      default:
        return {
          icon: 'bell',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
        };
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return t('marketing:facebookAds.notifications.minutesAgo', '{{count}} phút trước', { count: diffInMinutes });
    } else if (diffInMinutes < 24 * 60) {
      const hours = Math.floor(diffInMinutes / 60);
      return t('marketing:facebookAds.notifications.hoursAgo', '{{count}} giờ trước', { count: hours });
    } else {
      return date.toLocaleDateString('vi-VN');
    }
  };

  // Handle notification click
  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      onMarkAsRead?.(notification.id);
    }
    onNotificationClick?.(notification);
  };

  return (
    <>
      {/* Notification Bell Button */}
      <div className={className}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowModal(true)}
          className="relative"
        >
          <Icon name="bell" size="sm" />
          {unreadCount > 0 && (
            <Badge
              variant="danger"
              className="absolute -top-1 -right-1 h-5 w-5 text-xs p-0 flex items-center justify-center"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </div>

      {/* Notification Modal */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={t('marketing:facebookAds.notifications.title', 'Thông báo')}
        size="lg"
      >
        <div className="space-y-4">
          {/* Header with actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Typography variant="body2" className="text-muted-foreground">
                {t('marketing:facebookAds.notifications.total', 'Tổng cộng')}: {notifications.length}
              </Typography>
              {unreadCount > 0 && (
                <Badge variant="danger">
                  {unreadCount} {t('marketing:facebookAds.notifications.unread', 'chưa đọc')}
                </Badge>
              )}
            </div>
            
            {unreadCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={onMarkAllAsRead}
              >
                {t('marketing:facebookAds.notifications.markAllRead', 'Đánh dấu tất cả đã đọc')}
              </Button>
            )}
          </div>

          {/* Tabs */}
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={[
              {
                key: 'all',
                label: (
                  <span className="flex items-center">
                    {t('marketing:facebookAds.notifications.tabs.all', 'Tất cả')}
                  </span>
                ),
                children: (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {filteredNotifications.length === 0 ? (
                      <div className="text-center py-8">
                        <Icon name="bell-off" size="lg" className="text-muted-foreground mb-2" />
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('marketing:facebookAds.notifications.empty', 'Không có thông báo nào')}
                        </Typography>
                      </div>
                    ) : (
                      filteredNotifications.map((notification) => {
                        const config = getNotificationConfig(notification);

                        return (
                          <Card
                            key={notification.id}
                            variant="bordered"
                            className={`p-3 cursor-pointer transition-all hover:shadow-sm ${
                              !notification.isRead ? 'border-l-4 border-l-primary' : ''
                            } ${config.borderColor}`}
                            onClick={() => handleNotificationClick(notification)}
                          >
                            <div className="flex items-start space-x-3">
                              <div className={`p-2 rounded-full ${config.bgColor}`}>
                                <Icon name={config.icon as 'alert-triangle' | 'alert-circle' | 'info' | 'check-circle' | 'bell'} size="sm" className={config.color} />
                              </div>

                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between">
                                  <Typography
                                    variant="body2"
                                    className={`font-medium ${!notification.isRead ? 'text-foreground' : 'text-muted-foreground'}`}
                                  >
                                    {notification.title}
                                  </Typography>

                                  <div className="flex items-center space-x-1 ml-2">
                                    {notification.actionRequired && (
                                      <Badge variant="warning" className="text-xs">
                                        {t('marketing:facebookAds.notifications.actionRequired', 'Cần xử lý')}
                                      </Badge>
                                    )}
                                    {!notification.isRead && (
                                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                                    )}
                                  </div>
                                </div>

                                <Typography
                                  variant="body2"
                                  className={`mt-1 ${!notification.isRead ? 'text-foreground' : 'text-muted-foreground'}`}
                                >
                                  {notification.message}
                                </Typography>

                                <Typography variant="caption" className="text-muted-foreground mt-2 block">
                                  {formatTimestamp(notification.timestamp)}
                                </Typography>
                              </div>
                            </div>
                          </Card>
                        );
                      })
                    )}
                  </div>
                ),
              },
              {
                key: 'unread',
                label: (
                  <span className="flex items-center">
                    {t('marketing:facebookAds.notifications.tabs.unread', 'Chưa đọc')}
                    {unreadCount > 0 && (
                      <Badge variant="secondary" className="ml-1">
                        {unreadCount}
                      </Badge>
                    )}
                  </span>
                ),
                children: (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {filteredNotifications.length === 0 ? (
                      <div className="text-center py-8">
                        <Icon name="bell-off" size="lg" className="text-muted-foreground mb-2" />
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('marketing:facebookAds.notifications.empty', 'Không có thông báo nào')}
                        </Typography>
                      </div>
                    ) : (
                      filteredNotifications.map((notification) => {
                        const config = getNotificationConfig(notification);

                        return (
                          <Card
                            key={notification.id}
                            variant="bordered"
                            className={`p-3 cursor-pointer transition-all hover:shadow-sm ${
                              !notification.isRead ? 'border-l-4 border-l-primary' : ''
                            } ${config.borderColor}`}
                            onClick={() => handleNotificationClick(notification)}
                          >
                            <div className="flex items-start space-x-3">
                              <div className={`p-2 rounded-full ${config.bgColor}`}>
                                <Icon name={config.icon as 'alert-triangle' | 'alert-circle' | 'info' | 'check-circle' | 'bell'} size="sm" className={config.color} />
                              </div>

                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between">
                                  <Typography
                                    variant="body2"
                                    className={`font-medium ${!notification.isRead ? 'text-foreground' : 'text-muted-foreground'}`}
                                  >
                                    {notification.title}
                                  </Typography>

                                  <div className="flex items-center space-x-1 ml-2">
                                    {notification.actionRequired && (
                                      <Badge variant="warning" className="text-xs">
                                        {t('marketing:facebookAds.notifications.actionRequired', 'Cần xử lý')}
                                      </Badge>
                                    )}
                                    {!notification.isRead && (
                                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                                    )}
                                  </div>
                                </div>

                                <Typography
                                  variant="body2"
                                  className={`mt-1 ${!notification.isRead ? 'text-foreground' : 'text-muted-foreground'}`}
                                >
                                  {notification.message}
                                </Typography>

                                <Typography variant="caption" className="text-muted-foreground mt-2 block">
                                  {formatTimestamp(notification.timestamp)}
                                </Typography>
                              </div>
                            </div>
                          </Card>
                        );
                      })
                    )}
                  </div>
                ),
              },
              {
                key: 'alerts',
                label: (
                  <span className="flex items-center">
                    {t('marketing:facebookAds.notifications.tabs.alerts', 'Cảnh báo')}
                    {alertCount > 0 && (
                      <Badge variant="danger" className="ml-1">
                        {alertCount}
                      </Badge>
                    )}
                  </span>
                ),
                children: (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {filteredNotifications.length === 0 ? (
                      <div className="text-center py-8">
                        <Icon name="bell-off" size="lg" className="text-muted-foreground mb-2" />
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('marketing:facebookAds.notifications.empty', 'Không có thông báo nào')}
                        </Typography>
                      </div>
                    ) : (
                      filteredNotifications.map((notification) => {
                        const config = getNotificationConfig(notification);

                        return (
                          <Card
                            key={notification.id}
                            variant="bordered"
                            className={`p-3 cursor-pointer transition-all hover:shadow-sm ${
                              !notification.isRead ? 'border-l-4 border-l-primary' : ''
                            } ${config.borderColor}`}
                            onClick={() => handleNotificationClick(notification)}
                          >
                            <div className="flex items-start space-x-3">
                              <div className={`p-2 rounded-full ${config.bgColor}`}>
                                <Icon name={config.icon as 'alert-triangle' | 'alert-circle' | 'info' | 'check-circle' | 'bell'} size="sm" className={config.color} />
                              </div>

                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between">
                                  <Typography
                                    variant="body2"
                                    className={`font-medium ${!notification.isRead ? 'text-foreground' : 'text-muted-foreground'}`}
                                  >
                                    {notification.title}
                                  </Typography>

                                  <div className="flex items-center space-x-1 ml-2">
                                    {notification.actionRequired && (
                                      <Badge variant="warning" className="text-xs">
                                        {t('marketing:facebookAds.notifications.actionRequired', 'Cần xử lý')}
                                      </Badge>
                                    )}
                                    {!notification.isRead && (
                                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                                    )}
                                  </div>
                                </div>

                                <Typography
                                  variant="body2"
                                  className={`mt-1 ${!notification.isRead ? 'text-foreground' : 'text-muted-foreground'}`}
                                >
                                  {notification.message}
                                </Typography>

                                <Typography variant="caption" className="text-muted-foreground mt-2 block">
                                  {formatTimestamp(notification.timestamp)}
                                </Typography>
                              </div>
                            </div>
                          </Card>
                        );
                      })
                    )}
                  </div>
                ),
              },
              {
                key: 'warnings',
                label: t('marketing:facebookAds.notifications.tabs.warnings', 'Cảnh báo'),
                children: (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {filteredNotifications.length === 0 ? (
                      <div className="text-center py-8">
                        <Icon name="bell-off" size="lg" className="text-muted-foreground mb-2" />
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('marketing:facebookAds.notifications.empty', 'Không có thông báo nào')}
                        </Typography>
                      </div>
                    ) : (
                      filteredNotifications.map((notification) => {
                        const config = getNotificationConfig(notification);

                        return (
                          <Card
                            key={notification.id}
                            variant="bordered"
                            className={`p-3 cursor-pointer transition-all hover:shadow-sm ${
                              !notification.isRead ? 'border-l-4 border-l-primary' : ''
                            } ${config.borderColor}`}
                            onClick={() => handleNotificationClick(notification)}
                          >
                            <div className="flex items-start space-x-3">
                              <div className={`p-2 rounded-full ${config.bgColor}`}>
                                <Icon name={config.icon as 'alert-triangle' | 'alert-circle' | 'info' | 'check-circle' | 'bell'} size="sm" className={config.color} />
                              </div>

                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between">
                                  <Typography
                                    variant="body2"
                                    className={`font-medium ${!notification.isRead ? 'text-foreground' : 'text-muted-foreground'}`}
                                  >
                                    {notification.title}
                                  </Typography>

                                  <div className="flex items-center space-x-1 ml-2">
                                    {notification.actionRequired && (
                                      <Badge variant="warning" className="text-xs">
                                        {t('marketing:facebookAds.notifications.actionRequired', 'Cần xử lý')}
                                      </Badge>
                                    )}
                                    {!notification.isRead && (
                                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                                    )}
                                  </div>
                                </div>

                                <Typography
                                  variant="body2"
                                  className={`mt-1 ${!notification.isRead ? 'text-foreground' : 'text-muted-foreground'}`}
                                >
                                  {notification.message}
                                </Typography>

                                <Typography variant="caption" className="text-muted-foreground mt-2 block">
                                  {formatTimestamp(notification.timestamp)}
                                </Typography>
                              </div>
                            </div>
                          </Card>
                        );
                      })
                    )}
                  </div>
                ),
              },
            ]}
          />
        </div>
      </Modal>
    </>
  );
};

export default FacebookNotificationCenter;
