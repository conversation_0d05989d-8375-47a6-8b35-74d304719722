import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Icon,
  Loading,
} from '@/shared/components/common';
import { useFacebookAuth } from '../../hooks/facebook-ads/useFacebookAuth';
import { useRecentFacebookAdsCampaigns } from '../../hooks/facebook-ads/useFacebookAdsCampaigns';

interface FacebookQuickStatsProps {
  /**
   * Hiển thị loading state
   */
  isLoading?: boolean;
  
  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Facebook Ads Quick Stats Component
 * Hiển thị thống kê nhanh trong header
 */
const FacebookQuickStats: React.FC<FacebookQuickStatsProps> = ({
  isLoading = false,
  className = '',
}) => {
  const { t } = useTranslation(['marketing', 'common']);
  
  const {
    adAccounts,
    isLoading: authLoading,
  } = useFacebookAuth();

  const {
    data: recentCampaigns,
    isLoading: campaignsLoading,
  } = useRecentFacebookAdsCampaigns(10);

  // Loading state
  if (isLoading || authLoading || campaignsLoading) {
    return (
      <div className={`flex items-center space-x-4 ${className}`}>
        <Loading size="sm" />
        <Typography variant="caption" className="text-muted-foreground">
          {t('common:loading', 'Đang tải...')}
        </Typography>
      </div>
    );
  }

  // Calculate stats
  const totalAccounts = adAccounts.length;
  const activeAccounts = adAccounts.filter(acc => acc.accountStatus === 1).length;
  const totalCampaigns = recentCampaigns?.result?.items?.length || 0;
  const activeCampaigns = recentCampaigns?.result?.items?.filter(
    campaign => campaign.status === 'ACTIVE'
  ).length || 0;

  const stats = [
    {
      label: t('marketing:facebookAds.quickStats.accounts', 'Tài khoản'),
      value: `${activeAccounts}/${totalAccounts}`,
      icon: 'facebook',
      color: 'text-blue-600',
    },
    {
      label: t('marketing:facebookAds.quickStats.campaigns', 'Chiến dịch'),
      value: `${activeCampaigns}/${totalCampaigns}`,
      icon: 'campaign',
      color: 'text-orange-600',
    },
  ];

  return (
    <div className={`flex items-center space-x-6 ${className}`}>
      {stats.map((stat, index) => (
        <div key={index} className="flex items-center space-x-2">
          <Icon name={stat.icon as 'facebook' | 'campaign'} size="sm" className={stat.color} />
          <div>
            <Typography variant="caption" className="text-muted-foreground block">
              {stat.label}
            </Typography>
            <Typography variant="body2" className="font-medium">
              {stat.value}
            </Typography>
          </div>
        </div>
      ))}
    </div>
  );
};

export default FacebookQuickStats;
